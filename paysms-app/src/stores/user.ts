// stores/user.ts - 用户状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, BalanceResponse } from '@/types/api'
import { getUserBalance } from '@/services/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const balanceData = ref<BalanceResponse | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isAuthenticated = computed(() => userInfo.value !== null)

  const isKycVerified = computed(() => userInfo.value?.kyc === 1)

  const totalBalance = computed(() => userInfo.value?.coin || 0)

  const withdrawableBalance = computed(() => userInfo.value?.dcoin || 0)

  const unutilizedAmount = computed(() => userInfo.value?.ecoin || 0)

  const cashBonus = computed(() => userInfo.value?.bonus || 0)

  const formattedBalance = computed(() => {
    const balance = totalBalance.value
    return `₹${balance.toLocaleString('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`
  })

  // 动作
  const fetchBalance = async () => {
    try {
      loading.value = true
      error.value = null

      const data = await getUserBalance()
      balanceData.value = data
      userInfo.value = data.user

      console.log('Balance fetched successfully:', data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch balance'
      console.error('Failed to fetch balance:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchDepositBalance = async (amount: number = 200) => {
    try {
      loading.value = true
      error.value = null

      const { getDepositBalance } = await import('@/services/api')
      const data = await getDepositBalance(amount)
      balanceData.value = data
      userInfo.value = data.user

      console.log('Deposit balance fetched successfully:', data)
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch deposit balance'
      console.error('Failed to fetch deposit balance:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateUserInfo = (newUserInfo: UserInfo) => {
    userInfo.value = newUserInfo
  }

  const updateBalance = (newBalance: number) => {
    if (userInfo.value) {
      userInfo.value.coin = newBalance
      userInfo.value.totalcoin = newBalance
    }
  }

  const logout = () => {
    userInfo.value = null
    balanceData.value = null
    error.value = null
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    userInfo,
    balanceData,
    loading,
    error,

    // 计算属性
    isAuthenticated,
    isKycVerified,
    totalBalance,
    withdrawableBalance,
    unutilizedAmount,
    cashBonus,
    formattedBalance,

    // 动作
    fetchBalance,
    fetchDepositBalance,
    updateUserInfo,
    updateBalance,
    logout,
    clearError
  }
})
