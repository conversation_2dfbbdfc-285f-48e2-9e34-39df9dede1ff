<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <AppHeader :title="$t('home.title')" />

    <!-- Loading State -->
    <div v-if="userStore.loading" class="p-4 space-y-4">
      <SkeletonLoader type="balance" />
      <SkeletonLoader type="card" />
      <SkeletonLoader type="card" />
    </div>

    <!-- Error State -->
    <div v-else-if="userStore.error" class="p-4">
      <ErrorMessage :message="userStore.error" @retry="handleRetry" />
    </div>

    <!-- Main Content -->
    <div v-else class="pb-20">
      <!-- Balance Cards Section -->
      <div class="p-4 space-y-4">
        <!-- Total Balance Card -->
        <BalanceCard
          :title="$t('home.totalBalance')"
          :amount="userStore.totalBalance"
          :is-primary="true"
          class="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
        >
          <template #action>
            <ActionButton
              :text="$t('home.addCash')"
              variant="secondary"
              @click="navigateToDeposit"
            />
          </template>
        </BalanceCard>

        <!-- Amount Added (Unutilized) -->
        <BalanceCard
          :title="$t('home.amountAdded')"
          :amount="userStore.unutilizedAmount"
          class="bg-white border border-gray-200"
        />

        <!-- Winnings -->
        <BalanceCard
          :title="$t('home.winnings')"
          :amount="userStore.withdrawableBalance"
          class="bg-white border border-gray-200"
        >
          <template #subtitle v-if="!userStore.isKycVerified">
            <p class="text-sm text-gray-600 mt-1">
              {{ $t('home.verifyAccount') }}
            </p>
          </template>
          <template #action>
            <ActionButton
              v-if="userStore.isKycVerified"
              :text="$t('home.withdrawInstantly')"
              variant="primary"
              @click="navigateToWithdraw"
            />
            <ActionButton
              v-else
              :text="$t('home.verifyNow')"
              variant="outline"
              @click="handleVerifyAccount"
            />
          </template>
        </BalanceCard>

        <!-- Cash Bonus -->
        <BalanceCard
          :title="$t('home.cashBonus')"
          :amount="userStore.cashBonus"
          class="bg-white border border-gray-200"
        >
          <template #subtitle>
            <div class="mt-2 p-3 bg-blue-50 rounded-lg">
              <p class="text-xs text-blue-700">
                {{ $t('home.cashBonusRule') }}
                <button class="text-blue-600 underline ml-1" @click="showCashBonusInfo">
                  {{ $t('home.knowMore') }}
                </button>
              </p>
            </div>
          </template>
        </BalanceCard>
      </div>

      <!-- Navigation Section -->
      <div class="p-4 space-y-3">
        <!-- My Transactions -->
        <NavigationCard
          :title="$t('home.myTransactions')"
          icon="history"
          @click="navigateToTransactions"
        />

        <!-- Manage Payments -->
        <NavigationCard
          :title="$t('home.managePayments')"
          :subtitle="$t('payment.title')"
          icon="credit-card"
          @click="navigateToPayments"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import AppHeader from '@/components/layout/AppHeader.vue'
import BalanceCard from '@/components/balance/BalanceCard.vue'
import ActionButton from '@/components/common/ActionButton.vue'
import NavigationCard from '@/components/common/NavigationCard.vue'
import SkeletonLoader from '@/components/common/SkeletonLoader.vue'
import ErrorMessage from '@/components/common/ErrorMessage.vue'

const router = useRouter()
const userStore = useUserStore()

// 页面加载时获取用户余额
onMounted(async () => {
  await userStore.fetchBalance()
})

// 导航方法
const navigateToDeposit = () => {
  router.push('/pages/deposit/add/add')
}

const navigateToWithdraw = () => {
  router.push('/pages/withdraw/index')
}

const navigateToTransactions = () => {
  router.push('/pages/transaction/transaction')
}

const navigateToPayments = () => {
  router.push('/pages/deposit/managepayment/managepayment')
}

// 处理账户验证
const handleVerifyAccount = () => {
  // TODO: 实现账户验证逻辑
  console.log('Navigate to account verification')
}

// 显示现金奖金信息
const showCashBonusInfo = () => {
  // TODO: 显示现金奖金详细信息弹窗
  console.log('Show cash bonus info modal')
}

// 重试加载
const handleRetry = () => {
  userStore.clearError()
  userStore.fetchBalance()
}
</script>

<style scoped>
/* 自定义样式 */
.balance-card-enter-active,
.balance-card-leave-active {
  transition: all 0.3s ease;
}

.balance-card-enter-from,
.balance-card-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
