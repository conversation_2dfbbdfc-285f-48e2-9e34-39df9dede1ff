<template>
  <div
    :class="[
      'p-4 border rounded-xl transition-all duration-200',
      wallet.checked
        ? 'border-green-500 bg-green-50'
        : 'border-gray-200 bg-white hover:border-gray-300'
    ]"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <!-- Wallet Icon -->
        <div
          class="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center"
        >
          <img
            v-if="wallet.icon && !imageError"
            :src="getIconUrl(wallet.icon)"
            :alt="wallet.name"
            class="w-8 h-8 object-contain"
            @error="handleImageError"
          />
          <component v-else :is="getWalletIcon(wallet.name)" class="w-6 h-6 text-gray-600" />
        </div>

        <!-- Wallet Info -->
        <div>
          <h4 class="font-medium text-gray-900">{{ wallet.name }}</h4>
          <p class="text-sm text-gray-600">
            {{ getWalletDescription(wallet.name) }}
          </p>
        </div>
      </div>

      <!-- Status and Action -->
      <div class="flex items-center space-x-3">
        <!-- Status -->
        <div class="text-right">
          <div v-if="wallet.checked" class="flex items-center text-green-600 text-sm">
            <CheckCircle class="w-4 h-4 mr-1" />
            <span>{{ t('payment.linked') }}</span>
          </div>
          <div v-else class="text-sm text-gray-500">{{ t('payment.notLinked') }}</div>
        </div>

        <!-- Action Button -->
        <ActionButton
          v-if="!wallet.checked"
          :text="wallet.card"
          variant="outline"
          size="sm"
          @click="handleLink"
        />
        <button
          v-else
          @click="handleUnlink"
          class="text-sm text-red-600 hover:text-red-700 font-medium"
        >
          {{ t('payment.unlink') }}
        </button>
      </div>
    </div>

    <!-- Link Form (for Other UPI ID) -->
    <div v-if="showLinkForm" class="mt-4 pt-4 border-t border-gray-200">
      <div class="space-y-3">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{
            t('payment.upiId')
          }}</label>
          <input
            v-model="upiId"
            type="text"
            :placeholder="t('payment.upiPlaceholder')"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div class="flex space-x-2">
          <ActionButton
            :text="t('payment.link')"
            variant="primary"
            size="sm"
            :disabled="!upiId.trim()"
            @click="confirmLink"
          />
          <ActionButton :text="t('common.cancel')" variant="ghost" size="sm" @click="cancelLink" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { CheckCircle, Smartphone, CreditCard, Wallet } from 'lucide-vue-next'
import type { UpiWallet } from '@/types/api'
import ActionButton from '@/components/common/ActionButton.vue'

interface Props {
  wallet: UpiWallet
}

const props = defineProps<Props>()

const emit = defineEmits<{
  link: [wallet: UpiWallet, upiId?: string]
  unlink: [wallet: UpiWallet]
}>()

const { t } = useI18n()

// State
const showLinkForm = ref(false)
const upiId = ref('')
const imageError = ref(false)

// Computed
const isOtherUpi = computed(() => {
  return props.wallet.name.toLowerCase().includes('other')
})

// Methods
const getWalletIcon = (walletName: string) => {
  const name = walletName.toLowerCase()
  if (name.includes('paytm')) {
    return Wallet
  } else if (name.includes('phonepe') || name.includes('gpay')) {
    return Smartphone
  } else {
    return CreditCard
  }
}

const getWalletDescription = (walletName: string) => {
  const name = walletName.toLowerCase()
  if (name.includes('paytm')) {
    return t('payment.paytmDescription')
  } else if (name.includes('phonepe')) {
    return t('payment.phonepeDescription')
  } else if (name.includes('gpay')) {
    return t('payment.gpayDescription')
  } else if (name.includes('other')) {
    return t('payment.otherUpiDescription')
  } else {
    return t('payment.upiPaymentMethod')
  }
}

const getIconUrl = (iconPath: string) => {
  // Handle relative paths
  if (iconPath.startsWith('/')) {
    return `http://service.haiwailaba.cyou${iconPath}`
  }
  return iconPath
}

const handleImageError = () => {
  imageError.value = true
}

const handleLink = () => {
  if (isOtherUpi.value) {
    showLinkForm.value = true
  } else {
    emit('link', props.wallet)
  }
}

const handleUnlink = () => {
  emit('unlink', props.wallet)
}

const confirmLink = () => {
  if (upiId.value.trim()) {
    emit('link', props.wallet, upiId.value.trim())
    showLinkForm.value = false
    upiId.value = ''
  }
}

const cancelLink = () => {
  showLinkForm.value = false
  upiId.value = ''
}
</script>
