<template>
  <div :class="['rounded-lg p-4 border', variantClasses]">
    <div class="flex items-start">
      <!-- Icon -->
      <div class="flex-shrink-0">
        <component :is="iconComponent" :class="['w-5 h-5', iconColorClasses]" />
      </div>

      <!-- Content -->
      <div class="ml-3 flex-1">
        <!-- Title -->
        <h3 v-if="title" :class="['text-sm font-medium', titleColorClasses]">
          {{ title }}
        </h3>

        <!-- Message -->
        <p :class="['text-sm', title ? 'mt-1' : '', messageColorClasses]">
          {{ message }}
        </p>

        <!-- Actions -->
        <div v-if="showRetry || $slots.actions" class="mt-3 flex space-x-2">
          <button
            v-if="showRetry"
            @click="handleRetry"
            :class="[
              'text-sm font-medium rounded-md px-3 py-1.5 transition-colors',
              retryButtonClasses
            ]"
          >
            {{ retryText || t('common.retry') }}
          </button>

          <slot name="actions" />
        </div>
      </div>

      <!-- Close Button -->
      <div v-if="dismissible" class="ml-3 flex-shrink-0">
        <button
          @click="handleDismiss"
          :class="['rounded-md p-1.5 transition-colors', closeButtonClasses]"
        >
          <X class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { AlertCircle, CheckCircle, Info, AlertTriangle, X } from 'lucide-vue-next'

interface Props {
  message: string
  title?: string
  variant?: 'error' | 'warning' | 'info' | 'success'
  showRetry?: boolean
  retryText?: string
  dismissible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'error',
  showRetry: false,
  retryText: '',
  dismissible: false
})

const { t } = useI18n()

const emit = defineEmits<{
  retry: []
  dismiss: []
}>()

const iconComponents = {
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
  success: CheckCircle
}

const iconComponent = computed(() => {
  return iconComponents[props.variant]
})

const variantClasses = computed(() => {
  const variants = {
    error: 'bg-red-50 border-red-200',
    warning: 'bg-yellow-50 border-yellow-200',
    info: 'bg-blue-50 border-blue-200',
    success: 'bg-green-50 border-green-200'
  }
  return variants[props.variant]
})

const iconColorClasses = computed(() => {
  const colors = {
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500',
    success: 'text-green-500'
  }
  return colors[props.variant]
})

const titleColorClasses = computed(() => {
  const colors = {
    error: 'text-red-800',
    warning: 'text-yellow-800',
    info: 'text-blue-800',
    success: 'text-green-800'
  }
  return colors[props.variant]
})

const messageColorClasses = computed(() => {
  const colors = {
    error: 'text-red-700',
    warning: 'text-yellow-700',
    info: 'text-blue-700',
    success: 'text-green-700'
  }
  return colors[props.variant]
})

const retryButtonClasses = computed(() => {
  const colors = {
    error: 'bg-red-100 text-red-800 hover:bg-red-200',
    warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
    info: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
    success: 'bg-green-100 text-green-800 hover:bg-green-200'
  }
  return colors[props.variant]
})

const closeButtonClasses = computed(() => {
  const colors = {
    error: 'text-red-500 hover:bg-red-100',
    warning: 'text-yellow-500 hover:bg-yellow-100',
    info: 'text-blue-500 hover:bg-blue-100',
    success: 'text-green-500 hover:bg-green-100'
  }
  return colors[props.variant]
})

const handleRetry = () => {
  emit('retry')
}

const handleDismiss = () => {
  emit('dismiss')
}
</script>
