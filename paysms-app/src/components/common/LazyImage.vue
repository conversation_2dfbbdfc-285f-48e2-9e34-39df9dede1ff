<template>
  <div
    ref="containerRef"
    :class="['relative overflow-hidden', containerClass]"
    :style="{ aspectRatio: aspectRatio }"
  >
    <!-- Placeholder/Loading State -->
    <div
      v-if="!loaded || error"
      :class="[
        'absolute inset-0 flex items-center justify-center',
        'bg-gray-100 transition-opacity duration-300',
        loaded && !error ? 'opacity-0' : 'opacity-100'
      ]"
    >
      <div v-if="loading && !error" class="flex flex-col items-center space-y-2">
        <div class="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
        <span v-if="showLoadingText" class="text-xs text-gray-500">{{
          loadingText || t('common.loading')
        }}</span>
      </div>

      <div v-else-if="error" class="flex flex-col items-center space-y-2 text-gray-400">
        <ImageOff class="w-8 h-8" />
        <span v-if="showErrorText" class="text-xs">{{
          errorText || t('lazyImage.failedToLoad')
        }}</span>
      </div>

      <div v-else class="flex flex-col items-center space-y-2 text-gray-400">
        <Image class="w-8 h-8" />
        <span v-if="showPlaceholderText" class="text-xs">{{ placeholderText }}</span>
      </div>
    </div>

    <!-- Actual Image -->
    <Transition
      enter-active-class="transition-all duration-500 ease-out"
      enter-from-class="opacity-0 scale-105"
      enter-to-class="opacity-100 scale-100"
    >
      <img
        v-if="shouldLoad && !error"
        :src="src"
        :alt="alt"
        :class="[
          'absolute inset-0 w-full h-full transition-all duration-300',
          objectFit,
          loaded ? 'opacity-100' : 'opacity-0',
          imageClass
        ]"
        @load="handleLoad"
        @error="handleError"
        loading="lazy"
      />
    </Transition>

    <!-- Overlay Content -->
    <div v-if="$slots.overlay" class="absolute inset-0 flex items-center justify-center">
      <slot name="overlay" />
    </div>

    <!-- Retry Button -->
    <button
      v-if="error && retryable"
      @click="retry"
      class="absolute inset-0 flex items-center justify-center bg-black/10 hover:bg-black/20 transition-colors"
    >
      <div class="flex flex-col items-center space-y-1 text-white">
        <RotateCcw class="w-5 h-5" />
        <span class="text-xs">{{ retryText || t('common.retry') }}</span>
      </div>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Image, ImageOff, RotateCcw } from 'lucide-vue-next'

interface Props {
  src: string
  alt?: string
  aspectRatio?: string
  objectFit?: string
  containerClass?: string
  imageClass?: string
  placeholderText?: string
  loadingText?: string
  errorText?: string
  retryText?: string
  showLoadingText?: boolean
  showErrorText?: boolean
  showPlaceholderText?: boolean
  retryable?: boolean
  threshold?: number // Intersection threshold for lazy loading
  rootMargin?: string // Root margin for intersection observer
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  aspectRatio: 'auto',
  objectFit: 'object-cover',
  containerClass: '',
  imageClass: '',
  placeholderText: 'Image',
  showLoadingText: false,
  showErrorText: true,
  showPlaceholderText: false,
  retryable: true,
  threshold: 0.1,
  rootMargin: '50px'
})

const emit = defineEmits<{
  load: [event: Event]
  error: [event: Event]
  visible: []
}>()

const { t } = useI18n()

const containerRef = ref<HTMLElement>()
const loading = ref(false)
const loaded = ref(false)
const error = ref(false)
const shouldLoad = ref(false)
const retryCount = ref(0)

let observer: IntersectionObserver | null = null

const maxRetries = 3

const setupIntersectionObserver = () => {
  if (!containerRef.value || !('IntersectionObserver' in window)) {
    // Fallback: load immediately if IntersectionObserver is not supported
    shouldLoad.value = true
    return
  }

  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !shouldLoad.value) {
          shouldLoad.value = true
          loading.value = true
          emit('visible')

          // Stop observing once we start loading
          if (observer && containerRef.value) {
            observer.unobserve(containerRef.value)
          }
        }
      })
    },
    {
      threshold: props.threshold,
      rootMargin: props.rootMargin
    }
  )

  observer.observe(containerRef.value)
}

const handleLoad = (event: Event) => {
  loading.value = false
  loaded.value = true
  error.value = false
  emit('load', event)
}

const handleError = (event: Event) => {
  loading.value = false
  loaded.value = false
  error.value = true
  emit('error', event)
}

const retry = () => {
  if (retryCount.value >= maxRetries) return

  retryCount.value++
  error.value = false
  loading.value = true

  // Force reload by adding a timestamp to the URL
  const url = new URL(props.src, window.location.origin)
  url.searchParams.set('retry', retryCount.value.toString())

  // Create a new image element to test loading
  const img = new Image()
  img.onload = handleLoad
  img.onerror = handleError
  img.src = url.toString()
}

onMounted(() => {
  setupIntersectionObserver()
})

onUnmounted(() => {
  if (observer && containerRef.value) {
    observer.unobserve(containerRef.value)
  }
})

// Computed property to determine if we should show retry option
const canRetry = computed(() => {
  return props.retryable && retryCount.value < maxRetries
})
</script>
