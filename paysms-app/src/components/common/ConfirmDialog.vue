<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="visible"
        class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        @click="handleBackdropClick"
      >
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 scale-95 translate-y-4"
          enter-to-class="opacity-100 scale-100 translate-y-0"
          leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 scale-100 translate-y-0"
          leave-to-class="opacity-0 scale-95 translate-y-4"
        >
          <div v-if="visible" class="w-full max-w-sm bg-white rounded-2xl shadow-xl" @click.stop>
            <!-- Header -->
            <div class="p-6 pb-4">
              <div class="flex items-center space-x-3">
                <div
                  v-if="icon"
                  :class="[
                    'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center',
                    iconBgClasses
                  ]"
                >
                  <component :is="iconComponent" :class="['w-5 h-5', iconColorClasses]" />
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-900">
                    {{ title }}
                  </h3>
                  <p v-if="message" class="text-sm text-gray-600 mt-1">
                    {{ message }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Content -->
            <div v-if="$slots.default" class="px-6 pb-4">
              <slot />
            </div>

            <!-- Actions -->
            <div class="flex space-x-3 p-6 pt-4 border-t border-gray-100">
              <button
                v-if="showCancel"
                @click="handleCancel"
                :disabled="loading"
                class="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                {{ cancelText || t('common.cancel') }}
              </button>
              <button
                @click="handleConfirm"
                :disabled="loading"
                :class="[
                  'flex-1 px-4 py-2.5 text-sm font-medium rounded-xl transition-colors disabled:opacity-50',
                  'flex items-center justify-center space-x-2',
                  confirmButtonClasses
                ]"
              >
                <div
                  v-if="loading"
                  class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"
                />
                <span>{{ confirmText || t('common.confirm') }}</span>
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { AlertTriangle, AlertCircle, Info, CheckCircle, HelpCircle } from 'lucide-vue-next'

interface Props {
  title: string
  message?: string
  type?: 'warning' | 'danger' | 'info' | 'success' | 'question'
  icon?: boolean
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  loading?: boolean
  closeOnBackdrop?: boolean
}

const { t } = useI18n()

const props = withDefaults(defineProps<Props>(), {
  type: 'question',
  icon: true,
  confirmText: '',
  cancelText: '',
  showCancel: true,
  loading: false,
  closeOnBackdrop: true
})

const emit = defineEmits<{
  confirm: []
  cancel: []
  close: []
}>()

const visible = ref(false)

const iconComponents = {
  warning: AlertTriangle,
  danger: AlertCircle,
  info: Info,
  success: CheckCircle,
  question: HelpCircle
}

const iconComponent = computed(() => iconComponents[props.type])

const iconBgClasses = computed(() => {
  const classes = {
    warning: 'bg-yellow-100',
    danger: 'bg-red-100',
    info: 'bg-blue-100',
    success: 'bg-green-100',
    question: 'bg-gray-100'
  }
  return classes[props.type]
})

const iconColorClasses = computed(() => {
  const classes = {
    warning: 'text-yellow-600',
    danger: 'text-red-600',
    info: 'text-blue-600',
    success: 'text-green-600',
    question: 'text-gray-600'
  }
  return classes[props.type]
})

const confirmButtonClasses = computed(() => {
  const classes = {
    warning: 'bg-yellow-600 text-white hover:bg-yellow-700',
    danger: 'bg-red-600 text-white hover:bg-red-700',
    info: 'bg-blue-600 text-white hover:bg-blue-700',
    success: 'bg-green-600 text-white hover:bg-green-700',
    question: 'bg-blue-600 text-white hover:bg-blue-700'
  }
  return classes[props.type]
})

const show = () => {
  visible.value = true
}

const hide = () => {
  visible.value = false
  emit('close')
}

const handleConfirm = () => {
  emit('confirm')
  if (!props.loading) {
    hide()
  }
}

const handleCancel = () => {
  emit('cancel')
  hide()
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop && !props.loading) {
    handleCancel()
  }
}

defineExpose({
  show,
  hide
})
</script>
