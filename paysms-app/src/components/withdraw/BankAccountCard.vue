<template>
  <div
    @click="handleSelect"
    :class="[
      'p-4 border rounded-xl cursor-pointer transition-all duration-200',
      selected
        ? 'border-green-500 bg-green-50'
        : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
    ]"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <!-- Bank Icon -->
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          <Building2 class="w-6 h-6 text-blue-600" />
        </div>

        <!-- Bank Account Info -->
        <div>
          <h4 class="font-medium text-gray-900">{{ account.bankName }}</h4>
          <p class="text-sm text-gray-600">****{{ account.accountNumber.slice(-4) }}</p>
          <p class="text-xs text-gray-500">
            {{ account.accountHolderName }}
          </p>
        </div>
      </div>

      <!-- Status and Selection -->
      <div class="flex items-center space-x-3">
        <!-- Verification Status -->
        <div class="text-right">
          <div v-if="account.isVerified" class="flex items-center text-green-600 text-sm">
            <CheckCircle class="w-4 h-4 mr-1" />
            <span>{{ t('bankAccount.verified') }}</span>
          </div>
          <div v-else class="flex items-center text-orange-600 text-sm">
            <Clock class="w-4 h-4 mr-1" />
            <span>{{ t('bankAccount.pending') }}</span>
          </div>
        </div>

        <!-- Selection Indicator -->
        <div>
          <div
            v-if="selected"
            class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"
          >
            <Check class="w-4 h-4 text-white" />
          </div>
          <div v-else class="w-6 h-6 border-2 border-gray-300 rounded-full" />
        </div>
      </div>
    </div>

    <!-- Account Details (for selected account) -->
    <div v-if="selected" class="mt-3 pt-3 border-t border-green-200">
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-600">{{ t('bankAccount.accountNumber') }}:</span>
          <p class="font-medium">{{ formatAccountNumber(account.accountNumber) }}</p>
        </div>
        <div>
          <span class="text-gray-600">{{ t('bankAccount.ifscCode') }}:</span>
          <p class="font-medium">{{ account.ifscCode }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { Building2, CheckCircle, Clock, Check } from 'lucide-vue-next'
import { formatAccountNumber } from '@/utils/format'
import type { BankAccount } from '@/types/api'

interface Props {
  account: BankAccount
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

const emit = defineEmits<{
  select: [account: BankAccount]
}>()

const { t } = useI18n()

// Methods
const handleSelect = () => {
  emit('select', props.account)
}
</script>
