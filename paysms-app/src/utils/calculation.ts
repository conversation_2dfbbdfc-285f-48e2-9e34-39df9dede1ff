// utils/calculation.ts - 通用计算工具函数

import type { PaymentPage } from '@/types/api'

/**
 * 计算支付费用
 * @param amount 基础金额
 * @param rate 费率（小数形式，如0.03表示3%）
 * @param fixedFee 固定费用，默认0
 * @returns 计算后的费用
 */
export function calculatePaymentFee(amount: number, rate: number, fixedFee = 0): number {
  return (amount * rate) + fixedFee
}

/**
 * 计算支付总金额（包含费用或奖金）
 * @param amount 基础金额
 * @param channel 支付渠道信息
 * @returns 总金额
 */
export function calculatePaymentTotal(amount: number, channel: PaymentPage): number {
  if (amount === 0) return 0

  // 如果有费率，计算费用
  if (channel.rate > 0) {
    const fee = amount * channel.rate
    return amount + fee
  }

  // 如果有奖金，添加奖金
  if (channel.discoin > 0) {
    return amount + channel.discoin
  }

  // 没有额外费用或奖金
  return amount
}

/**
 * 计算费用显示金额
 * @param amount 基础金额
 * @param channel 支付渠道信息
 * @returns 费用金额（正数表示费用，负数表示奖金）
 */
export function calculateFeeAmount(amount: number, channel: PaymentPage): number {
  if (amount === 0) return 0

  // 如果有费率，返回费用
  if (channel.rate > 0) {
    return amount * channel.rate
  }

  // 如果有奖金，返回负数表示奖金
  if (channel.discoin > 0) {
    return -channel.discoin
  }

  return 0
}

/**
 * 验证金额是否在指定范围内
 * @param amount 金额
 * @param min 最小值
 * @param max 最大值
 * @returns 验证结果
 */
export function validateAmountRange(amount: number, min: number, max: number): {
  isValid: boolean
  error?: string
} {
  if (amount < min) {
    return {
      isValid: false,
      error: `Amount must be at least ₹${min}`
    }
  }

  if (amount > max) {
    return {
      isValid: false,
      error: `Amount cannot exceed ₹${max}`
    }
  }

  return { isValid: true }
}

/**
 * 计算提现手续费
 * @param amount 提现金额
 * @param feeRate 手续费率，默认0（无手续费）
 * @param minFee 最小手续费，默认0
 * @param maxFee 最大手续费，默认无限制
 * @returns 手续费金额
 */
export function calculateWithdrawFee(
  amount: number,
  feeRate = 0,
  minFee = 0,
  maxFee = Infinity
): number {
  const calculatedFee = amount * feeRate
  return Math.min(Math.max(calculatedFee, minFee), maxFee)
}

/**
 * 计算实际到账金额
 * @param amount 提现金额
 * @param feeRate 手续费率
 * @param minFee 最小手续费
 * @param maxFee 最大手续费
 * @returns 实际到账金额
 */
export function calculateNetAmount(
  amount: number,
  feeRate = 0,
  minFee = 0,
  maxFee = Infinity
): number {
  const fee = calculateWithdrawFee(amount, feeRate, minFee, maxFee)
  return amount - fee
}

/**
 * 计算奖金使用限制
 * @param entryFee 报名费
 * @param bonusPercentage 奖金使用百分比，默认10%
 * @returns 可使用的奖金金额
 */
export function calculateBonusLimit(entryFee: number, bonusPercentage = 0.1): number {
  return entryFee * bonusPercentage
}

/**
 * 计算复合利率
 * @param principal 本金
 * @param rate 利率（年化）
 * @param time 时间（年）
 * @param compoundFrequency 复利频率（每年复利次数），默认1
 * @returns 复利后的金额
 */
export function calculateCompoundInterest(
  principal: number,
  rate: number,
  time: number,
  compoundFrequency = 1
): number {
  return principal * Math.pow(1 + rate / compoundFrequency, compoundFrequency * time)
}

/**
 * 计算百分比变化
 * @param oldValue 旧值
 * @param newValue 新值
 * @returns 百分比变化（小数形式）
 */
export function calculatePercentageChange(oldValue: number, newValue: number): number {
  if (oldValue === 0) return newValue > 0 ? 1 : 0
  return (newValue - oldValue) / oldValue
}

/**
 * 四舍五入到指定小数位
 * @param num 数字
 * @param decimals 小数位数，默认2位
 * @returns 四舍五入后的数字
 */
export function roundToDecimals(num: number, decimals = 2): number {
  return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
}
