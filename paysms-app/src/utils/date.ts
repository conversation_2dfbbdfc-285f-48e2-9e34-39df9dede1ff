// utils/date.ts - 日期格式化工具函数

/**
 * 格式化相对日期显示
 * @param dateString 日期字符串
 * @param t 国际化函数
 * @returns 格式化后的日期字符串
 */
export function formatRelativeDate(dateString: string, t: (key: string) => string): string {
  const date = new Date(dateString)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (date.toDateString() === today.toDateString()) {
    return t('common.today')
  } else if (date.toDateString() === yesterday.toDateString()) {
    return t('common.yesterday')
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }
}

/**
 * 获取交易日期
 * @param transaction 交易对象
 * @param fallbackDate 备用日期，当交易对象没有日期信息时使用
 * @returns 日期字符串 (MM/dd/yyyy 格式)
 */
export function getTransactionDate(transaction: any, fallbackDate?: string | Date): string {
  // 1. 如果交易对象有 created_at 或 date 字段，优先使用
  if (transaction.created_at) {
    const date = new Date(transaction.created_at)
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    })
  }

  if (transaction.date) {
    const date = new Date(transaction.date)
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    })
  }

  // 2. 如果有备用日期，使用备用日期
  if (fallbackDate) {
    const date = typeof fallbackDate === 'string' ? new Date(fallbackDate) : fallbackDate
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    })
  }

  // 3. 最后使用今天的日期作为默认值
  const today = new Date()
  return today.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  })
}

/**
 * 格式化日期为本地化字符串
 * @param date 日期对象或字符串
 * @param locale 本地化设置
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: Date | string,
  locale = 'en-US',
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString(locale, options)
}

/**
 * 检查日期是否为今天
 * @param date 日期对象或字符串
 * @returns 是否为今天
 */
export function isToday(date: Date | string): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const today = new Date()
  return dateObj.toDateString() === today.toDateString()
}

/**
 * 检查日期是否为昨天
 * @param date 日期对象或字符串
 * @returns 是否为昨天
 */
export function isYesterday(date: Date | string): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return dateObj.toDateString() === yesterday.toDateString()
}

/**
 * 从分组的交易数据中提取日期键
 * 用于处理 API 返回的按日期分组的交易记录
 * @param groupedTransactions 按日期分组的交易记录
 * @returns 日期键数组，按时间倒序排列
 */
export function extractDateKeysFromGroupedTransactions(
  groupedTransactions: Record<string, any[]>
): string[] {
  const dateKeys = Object.keys(groupedTransactions)

  // 尝试解析日期并排序（最新的在前）
  return dateKeys.sort((a, b) => {
    const dateA = new Date(a)
    const dateB = new Date(b)

    // 如果日期解析失败，保持原顺序
    if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
      return 0
    }

    return dateB.getTime() - dateA.getTime()
  })
}

/**
 * 智能获取交易日期，支持从分组数据中推断
 * @param transaction 交易对象
 * @param groupKey 分组键（如果交易来自分组数据）
 * @returns 日期字符串
 */
export function getSmartTransactionDate(transaction: any, groupKey?: string): string {
  // 1. 如果提供了分组键，直接使用（这是最准确的）
  if (groupKey) {
    // 尝试解析分组键为日期
    const groupDate = new Date(groupKey)
    if (!isNaN(groupDate.getTime())) {
      return groupDate.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      })
    }
    // 如果分组键已经是正确格式，直接返回
    return groupKey
  }

  // 2. 否则使用原有的逻辑
  return getTransactionDate(transaction)
}
