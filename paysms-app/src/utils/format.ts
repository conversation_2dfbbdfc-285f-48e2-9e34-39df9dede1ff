// utils/format.ts - 通用格式化工具函数

/**
 * 格式化账户号码，隐藏中间部分
 * @param accountNumber 账户号码
 * @param showFirst 显示前几位，默认4位
 * @param showLast 显示后几位，默认4位
 * @param maskChar 遮罩字符，默认*
 * @returns 格式化后的账户号码
 */
export function formatAccountNumber(
  accountNumber: string,
  showFirst = 4,
  showLast = 4,
  maskChar = '*'
): string {
  if (accountNumber.length <= showFirst + showLast) {
    return accountNumber
  }

  const first = accountNumber.slice(0, showFirst)
  const last = accountNumber.slice(-showLast)
  const middle = maskChar.repeat(Math.max(4, accountNumber.length - showFirst - showLast))

  return `${first}${middle}${last}`
}

/**
 * 格式化订单ID，显示前后部分
 * @param orderId 订单ID
 * @param showFirst 显示前几位，默认8位
 * @param showLast 显示后几位，默认4位
 * @returns 格式化后的订单ID
 */
export function formatOrderId(orderId: string, showFirst = 8, showLast = 4): string {
  if (orderId.length <= showFirst + showLast) {
    return orderId
  }
  return `${orderId.slice(0, showFirst)}...${orderId.slice(-showLast)}`
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的文件大小
 */
export function formatFileSize(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化百分比
 * @param value 数值（0-1之间）
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number, decimals = 2): string {
  return `${(value * 100).toFixed(decimals)}%`
}

/**
 * 格式化手机号码
 * @param phoneNumber 手机号码
 * @param countryCode 国家代码，默认+91（印度）
 * @returns 格式化后的手机号码
 */
export function formatPhoneNumber(phoneNumber: string, countryCode = '+91'): string {
  // 移除所有非数字字符
  const cleaned = phoneNumber.replace(/\D/g, '')
  
  // 如果已经包含国家代码，直接返回
  if (cleaned.length > 10) {
    return `+${cleaned}`
  }
  
  // 添加国家代码
  return `${countryCode} ${cleaned}`
}

/**
 * 截断文本并添加省略号
 * @param text 原始文本
 * @param maxLength 最大长度
 * @param suffix 后缀，默认...
 * @returns 截断后的文本
 */
export function truncateText(text: string, maxLength: number, suffix = '...'): string {
  if (text.length <= maxLength) {
    return text
  }
  return text.slice(0, maxLength - suffix.length) + suffix
}

/**
 * 格式化数字，添加千位分隔符
 * @param num 数字
 * @param locale 本地化设置，默认en-IN
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number, locale = 'en-IN'): string {
  return num.toLocaleString(locale)
}

/**
 * 格式化银行名称（从IFSC代码提取）
 * @param ifscCode IFSC代码
 * @returns 银行名称
 */
export function formatBankName(ifscCode: string): string {
  // 简单的银行代码映射，实际应该使用完整的银行数据库
  const bankCodes: Record<string, string> = {
    'SBIN': 'State Bank of India',
    'HDFC': 'HDFC Bank',
    'ICIC': 'ICICI Bank',
    'AXIS': 'Axis Bank',
    'PUNB': 'Punjab National Bank',
    'UBIN': 'Union Bank of India',
    'CNRB': 'Canara Bank',
    'BARB': 'Bank of Baroda'
  }

  const bankCode = ifscCode.slice(0, 4)
  return bankCodes[bankCode] || 'Unknown Bank'
}
